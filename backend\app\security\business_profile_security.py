"""
Business Profile Security Module

This module provides security functions and validation for business profile operations.
"""

import logging
import re
import uuid
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from ..models.business_profile import BusinessProfile, BusinessProfileDataSourceAssignment

logger = logging.getLogger(__name__)

# Security configuration
class BusinessProfileSecurityConfig:
    """Security configuration for business profiles."""
    MAX_PROFILES_PER_USER = 10
    MAX_DATA_SOURCES_PER_PROFILE = 50
    MAX_TEXT_LENGTH = 1000
    MAX_METADATA_SIZE = 10000  # bytes

def validate_uuid_format(uuid_string: str, field_name: str = "UUID") -> str:
    """Validate UUID format and return the validated UUID string."""
    try:
        # Parse and validate UUID
        parsed_uuid = uuid.UUID(uuid_string)
        return str(parsed_uuid)
    except (ValueError, TypeError) as e:
        logger.warning(f"Invalid {field_name} format: {uuid_string}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid {field_name} format"
        )

def validate_text_input(
    text: str, 
    field_name: str, 
    max_length: int = BusinessProfileSecurityConfig.MAX_TEXT_LENGTH,
    required: bool = True
) -> str:
    """Validate and sanitize text input."""
    if not text and required:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} is required"
        )
    
    if not text:
        return ""
    
    # Check length
    if len(text) > max_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} exceeds maximum length of {max_length} characters"
        )
    
    # Basic sanitization - remove control characters
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Trim whitespace
    sanitized = sanitized.strip()
    
    return sanitized

def validate_metadata(metadata: Dict[str, Any], field_name: str = "metadata") -> Dict[str, Any]:
    """Validate metadata dictionary."""
    if not metadata:
        return {}
    
    # Check size (rough estimate)
    metadata_str = str(metadata)
    if len(metadata_str.encode('utf-8')) > BusinessProfileSecurityConfig.MAX_METADATA_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} exceeds maximum size limit"
        )
    
    # Validate keys and values
    validated_metadata = {}
    for key, value in metadata.items():
        if not isinstance(key, str):
            continue
        
        # Sanitize key
        clean_key = validate_text_input(key, f"{field_name} key", 100, False)
        if not clean_key:
            continue
        
        # Handle different value types
        if isinstance(value, str):
            clean_value = validate_text_input(value, f"{field_name} value", 500, False)
            validated_metadata[clean_key] = clean_value
        elif isinstance(value, (int, float, bool)):
            validated_metadata[clean_key] = value
        elif isinstance(value, (list, dict)):
            # For complex types, convert to string and validate
            clean_value = validate_text_input(str(value), f"{field_name} value", 1000, False)
            validated_metadata[clean_key] = clean_value
    
    return validated_metadata

def check_user_profile_limit(db: Session, user_id: int) -> None:
    """Check if user has reached the maximum number of profiles."""
    profile_count = db.query(BusinessProfile).filter(
        BusinessProfile.user_id == user_id
    ).count()
    
    if profile_count >= BusinessProfileSecurityConfig.MAX_PROFILES_PER_USER:
        logger.warning(f"User {user_id} exceeded profile limit: {profile_count}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Maximum number of profiles ({BusinessProfileSecurityConfig.MAX_PROFILES_PER_USER}) reached"
        )

def check_profile_data_source_limit(db: Session, profile_id: str) -> None:
    """Check if profile has reached the maximum number of data sources."""
    data_source_count = db.query(BusinessProfileDataSourceAssignment).filter(
        BusinessProfileDataSourceAssignment.business_profile_id == profile_id
    ).count()
    
    if data_source_count >= BusinessProfileSecurityConfig.MAX_DATA_SOURCES_PER_PROFILE:
        logger.warning(f"Profile {profile_id} exceeded data source limit: {data_source_count}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Maximum number of data sources ({BusinessProfileSecurityConfig.MAX_DATA_SOURCES_PER_PROFILE}) reached"
        )

def sanitize_log_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Sanitize data for logging to prevent log injection."""
    sanitized = {}
    for key, value in data.items():
        if isinstance(value, str):
            # Remove newlines and control characters
            clean_value = re.sub(r'[\r\n\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', str(value))
            sanitized[key] = clean_value[:100]  # Limit length
        else:
            sanitized[key] = str(value)[:100]
    return sanitized

def security_audit_log(
    action: str, 
    user_id: int, 
    resource_id: str, 
    metadata: Optional[Dict[str, Any]] = None
) -> None:
    """Log security-relevant actions for audit purposes."""
    try:
        sanitized_metadata = sanitize_log_data(metadata or {})
        logger.info(
            f"Security audit: action={action}, user_id={user_id}, "
            f"resource_id={resource_id}, metadata={sanitized_metadata}"
        )
    except Exception as e:
        logger.error(f"Error logging security audit: {e}")

def validate_business_profile_access(
    db: Session, 
    profile_id: str, 
    user_id: int, 
    operation: str = "access"
) -> BusinessProfile:
    """Validate that a user has access to a business profile."""
    profile = db.query(BusinessProfile).filter(
        BusinessProfile.id == profile_id,
        BusinessProfile.user_id == user_id
    ).first()
    
    if not profile:
        logger.warning(f"Access denied: User {user_id} attempted {operation} on profile {profile_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Business profile not found or access denied"
        )
    
    return profile

def validate_data_source_assignment_access(
    db: Session,
    profile_id: str,
    data_source_id: str,
    user_id: int,
    operation: str = "access"
) -> BusinessProfileDataSourceAssignment:
    """Validate that a user has access to a data source assignment."""
    # First validate profile access
    profile = validate_business_profile_access(db, profile_id, user_id, operation)
    
    # Then find the assignment
    assignment = db.query(BusinessProfileDataSourceAssignment).filter(
        BusinessProfileDataSourceAssignment.business_profile_id == profile_id,
        BusinessProfileDataSourceAssignment.data_source_id == data_source_id
    ).first()
    
    if not assignment:
        logger.warning(f"Assignment not found: profile {profile_id}, data_source {data_source_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source assignment not found"
        )
    
    return assignment
