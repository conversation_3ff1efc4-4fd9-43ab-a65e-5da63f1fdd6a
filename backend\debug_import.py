#!/usr/bin/env python3
"""
Debug import issues in business profile service
"""

import sys
import traceback
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_import(module_path, description):
    """Test importing a specific module."""
    try:
        exec(f"from {module_path} import *")
        print(f"✅ {description}: SUCCESS")
        return True
    except Exception as e:
        print(f"❌ {description}: FAILED")
        print(f"   Error: {type(e).__name__}: {e}")
        traceback.print_exc()
        print()
        return False

def main():
    """Test all imports used by business profile service."""
    print("🔍 Testing business profile service imports...\n")
    
    # Test basic imports
    test_import("typing", "Basic typing module")
    test_import("sqlalchemy.orm", "SQLAlchemy ORM")
    test_import("sqlalchemy", "SQLAlchemy core")
    
    # Test app imports
    test_import("app.database", "Database models")
    test_import("app.models.business_profile", "Business profile models")
    test_import("app.security.business_profile_security", "Business profile security")
    
    # Test agent imports
    test_import("agents.utils.knowledge_graph_service", "Knowledge graph service")
    
    # Test service imports
    test_import("app.services.business_profile_template_service", "Business profile template service")
    
    # Test the full business profile service
    test_import("app.services.business_profile_service", "Full business profile service")
    
    print("\n🏁 Import testing completed!")

if __name__ == "__main__":
    main()
