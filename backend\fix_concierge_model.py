#!/usr/bin/env python3
"""
Fix Concierge Agent Model Configuration

This script ensures that the concierge-agent persona is properly configured
in the database with the correct model settings from the admin panel.
"""

import logging
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import Base, Persona, get_utc_now
from app.config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_concierge_agent_model():
    """Fix the concierge-agent persona model configuration."""
    
    # Create database engine and session
    engine = create_engine(
        config.DATABASE_URL,
        echo=False,
        connect_args={"check_same_thread": False} if config.DATABASE_URL.startswith("sqlite") else {},
    )
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if concierge-agent exists
        concierge_persona = db.query(Persona).filter(Persona.id == "concierge-agent").first()
        
        if concierge_persona:
            logger.info(f"Found existing concierge-agent persona:")
            logger.info(f"  Current provider: {concierge_persona.provider}")
            logger.info(f"  Current model: {concierge_persona.model}")
            
            # Update with the correct model configuration
            # Based on your admin panel screenshot, you selected Moonshot/Kimi K2 Instruct
            concierge_persona.provider = "moonshot"
            concierge_persona.model = "moonshot-v1-8k"  # This is the typical model name for Kimi K2 Instruct
            concierge_persona.updated_at = get_utc_now()
            
            db.commit()
            db.refresh(concierge_persona)
            
            logger.info(f"Updated concierge-agent persona:")
            logger.info(f"  New provider: {concierge_persona.provider}")
            logger.info(f"  New model: {concierge_persona.model}")
            
        else:
            logger.info("concierge-agent persona not found in database. Creating it...")
            
            # Create the concierge-agent persona with the correct configuration
            concierge_data = {
                "id": "concierge-agent",
                "name": "Datagenius Concierge",
                "description": "Your knowledgeable guide to Datagenius AI personas with conversational assistance and advanced coordination capabilities",
                "industry": "Technology",
                "skills": [
                    "Conversational Assistance",
                    "Question Answering", 
                    "Guidance",
                    "Persona Recommendation",
                    "Data Assistance",
                    "Workflow Coordination"
                ],
                "rating": 4.9,
                "review_count": 150,
                "image_url": "/placeholder.svg",
                "price": 0.0,  # Free for all users
                "provider": "moonshot",
                "model": "moonshot-v1-8k",  # Kimi K2 Instruct model
                "is_active": True,
                "age_restriction": 0,
                "content_filters": None,
                "created_at": get_utc_now(),
                "updated_at": get_utc_now()
            }
            
            new_persona = Persona(**concierge_data)
            db.add(new_persona)
            db.commit()
            db.refresh(new_persona)
            
            logger.info(f"Created concierge-agent persona:")
            logger.info(f"  Provider: {new_persona.provider}")
            logger.info(f"  Model: {new_persona.model}")
        
        logger.info("✅ Concierge agent model configuration fixed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error fixing concierge agent model: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def list_all_personas():
    """List all personas in the database for debugging."""
    
    # Create database engine and session
    engine = create_engine(
        config.DATABASE_URL,
        echo=False,
        connect_args={"check_same_thread": False} if config.DATABASE_URL.startswith("sqlite") else {},
    )
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        personas = db.query(Persona).all()
        
        logger.info(f"Found {len(personas)} personas in database:")
        for persona in personas:
            logger.info(f"  - {persona.id}: {persona.name} (provider: {persona.provider}, model: {persona.model})")
            
    except Exception as e:
        logger.error(f"Error listing personas: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("🔧 Starting concierge agent model configuration fix...")
    
    # First, list all personas for debugging
    logger.info("📋 Current personas in database:")
    list_all_personas()
    
    # Fix the concierge agent model
    fix_concierge_agent_model()
    
    # List personas again to confirm the fix
    logger.info("📋 Personas after fix:")
    list_all_personas()
    
    logger.info("🎉 Fix completed!")
