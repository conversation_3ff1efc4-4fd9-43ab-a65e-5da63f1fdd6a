/**
 * Business Profile Selector Component
 * 
 * Dropdown component for selecting and switching between business profiles.
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Check, ChevronDown, Plus, Building2, Loader2, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { BusinessProfile, businessProfileApi, getBusinessTypeLabel } from '@/lib/businessProfileApi';
import { useToast } from '@/hooks/use-toast';
import { useBusinessProfileRefresh } from '@/hooks/use-business-profile-refresh';
import { useBusinessProfileStore } from '@/stores/business-profile-store';


interface BusinessProfileSelectorProps {
  onProfileChange?: (profile: BusinessProfile | null) => void;
  onCreateNew?: () => void;
  className?: string;
  showCreateButton?: boolean;
}



export const BusinessProfileSelector: React.FC<BusinessProfileSelectorProps> = ({
  onProfileChange,
  onCreateNew,
  className,
  showCreateButton = true
}) => {
  const [open, setOpen] = useState(false);
  const [isSwitching, setIsSwitching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');

  // Use the shared business profile hook
  const { profiles: rawProfiles, activeProfile, isLoading, switchProfile, refreshProfiles } = useBusinessProfile();

  // Ensure profiles is always an array to prevent iteration errors
  const profiles = Array.isArray(rawProfiles) ? rawProfiles : [];

  // Create a stable array for profile iteration
  const stableProfiles = useMemo(() => {
    return Array.isArray(profiles) ? profiles : [];
  }, [profiles]);
  const { toast } = useToast();

  // Notify parent component when active profile changes
  useEffect(() => {
    if (onProfileChange) {
      onProfileChange(activeProfile);
    }
  }, [activeProfile, onProfileChange]);

  const handleProfileSelect = async (profile: BusinessProfile) => {
    if (profile.id === activeProfile?.id) {
      setOpen(false);
      return;
    }

    try {
      setIsSwitching(true);
      await switchProfile(profile.id);
      setOpen(false);
    } catch (error) {
      console.error('Error switching profile:', error);
      // Error handling is done in the shared hook
    } finally {
      setIsSwitching(false);
    }
  };

  const handleCreateNew = () => {
    setOpen(false);
    if (onCreateNew) {
      onCreateNew();
    }
  };

  // Filter out any invalid profiles
  const validProfiles = useMemo(() => {
    return stableProfiles.filter(profile => {
      const isValid = profile &&
                     typeof profile === 'object' &&
                     typeof profile.id === 'string' &&
                     typeof profile.name === 'string' &&
                     profile.id.length > 0 &&
                     profile.name.length > 0;

      if (!isValid) {
        console.warn('🔍 Filtering out invalid profile:', profile);
      }

      return isValid;
    });
  }, [stableProfiles]);

  if (isLoading && validProfiles.length === 0) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Loading profiles...</span>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex items-center gap-2 px-3 py-2 border border-destructive rounded-md bg-destructive/10">
          <Building2 className="h-4 w-4 text-destructive" />
          <span className="text-sm text-destructive">Error loading profiles</span>
          <Button
            size="sm"
            variant="outline"
            className="ml-2 h-6 text-xs"
            onClick={() => {
              setError(null);
              refreshProfiles();
            }}
          >
            Refresh
          </Button>
        </div>
      </div>
    );
  }

  // Show create profile button if no profiles exist and not loading
  if (!isLoading && validProfiles.length === 0) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Button
          variant="outline"
          onClick={handleCreateNew}
          className="w-[300px] justify-start"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Your First Business Profile
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Popover
        open={open}
        onOpenChange={(newOpen) => {
          // Don't open if data is loading
          if (newOpen && isLoading) {
            return;
          }
          setOpen(newOpen);
        }}
      >
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-[300px] justify-between"
            disabled={isSwitching}
          >
            <div className="flex items-center gap-2 min-w-0">
              <Building2 className="h-4 w-4 flex-shrink-0" />
              {activeProfile ? (
                <div className="flex items-center gap-2 min-w-0">
                  <span className="truncate">{activeProfile.name}</span>
                  {activeProfile.business_type && (
                    <Badge variant="secondary" className="text-xs">
                      {activeProfile.business_type}
                    </Badge>
                  )}
                </div>
              ) : (
                <span className="text-muted-foreground">Select business profile</span>
              )}
            </div>
            {isSwitching ? (
              <Loader2 className="ml-2 h-4 w-4 animate-spin flex-shrink-0" />
            ) : (
              <ChevronDown className="ml-2 h-4 w-4 flex-shrink-0 opacity-50" />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <div className="flex flex-col max-h-[300px]">
            {/* Search Input */}
            <div className="flex items-center border-b px-3 py-2">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                className="flex h-8 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Search profiles..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
              />
            </div>

            {/* Content */}
            <div className="overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center">
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm text-muted-foreground">Loading profiles...</span>
                  </div>
                </div>
              ) : error ? (
                <div className="p-4 text-center">
                  <span className="text-sm text-destructive">Error loading profiles</span>
                  <Button size="sm" variant="outline" className="mt-2" onClick={refreshProfiles}>
                    Try Again
                  </Button>
                </div>
              ) : validProfiles.length === 0 ? (
                <div className="p-4 text-center">
                  <p className="text-sm text-muted-foreground mb-2">No profiles found</p>
                  {showCreateButton && (
                    <Button size="sm" onClick={handleCreateNew}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Profile
                    </Button>
                  )}
                </div>
              ) : (
                <div className="p-1">
                  {validProfiles
                    .filter(profile => {
                      if (!searchValue) return true;
                      const searchLower = searchValue.toLowerCase();
                      return (
                        profile.name?.toLowerCase().includes(searchLower) ||
                        profile.business_type?.toLowerCase().includes(searchLower) ||
                        profile.industry?.toLowerCase().includes(searchLower)
                      );
                    })
                    .map((profile) => {
                      // Validate profile before rendering
                      if (!profile ||
                          typeof profile !== 'object' ||
                          !profile.id ||
                          !profile.name ||
                          typeof profile.id !== 'string' ||
                          typeof profile.name !== 'string') {
                        return null;
                      }

                      // Create safe display values
                      const displayName = profile.name || 'Unnamed Profile';
                      const displayBusinessType = (profile.business_type && typeof profile.business_type === 'string')
                        ? profile.business_type : null;
                      const displayIndustry = (profile.industry && typeof profile.industry === 'string')
                        ? profile.industry : null;
                      const displayDataSourceCount = (typeof profile.data_source_count === 'number' && profile.data_source_count > 0)
                        ? profile.data_source_count : null;
                      const isActiveProfile = profile.is_active === true;

                      return (
                        <div
                          key={profile.id}
                          className={cn(
                            "flex items-center justify-between px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm",
                            isActiveProfile && "bg-accent text-accent-foreground"
                          )}
                          onClick={() => handleProfileSelect(profile)}
                        >
                          <div className="flex items-center gap-2 min-w-0">
                            <Building2 className="h-4 w-4 flex-shrink-0" />
                            <div className="min-w-0">
                              <div className="flex items-center gap-2">
                                <span className="truncate">{displayName}</span>
                                {displayBusinessType && (
                                  <Badge variant="outline" className="text-xs">
                                    {displayBusinessType}
                                  </Badge>
                                )}
                              </div>
                              {displayIndustry && (
                                <p className="text-xs text-muted-foreground truncate">
                                  {displayIndustry}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {displayDataSourceCount && (
                              <Badge variant="secondary" className="text-xs">
                                {displayDataSourceCount} sources
                              </Badge>
                            )}
                            {isActiveProfile && (
                              <Check className="h-4 w-4 text-primary" />
                            )}
                          </div>
                        </div>
                      );
                    }).filter(Boolean)}
                </div>
              )}
            </div>

            {/* Create New Profile Button */}
            {showCreateButton && (
              <div className="border-t p-2">
                <Button
                  size="sm"
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={handleCreateNew}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Profile
                </Button>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
      
      {activeProfile && (
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <span>•</span>
          <span>{profiles.length} profile{profiles.length !== 1 ? 's' : ''}</span>
          {activeProfile.data_source_count !== undefined && (
            <>
              <span>•</span>
              <span>{activeProfile.data_source_count} data source{activeProfile.data_source_count !== 1 ? 's' : ''}</span>
            </>
          )}
        </div>
      )}
    </div>
  );
};

// Shared business profile hook that manages all profile state
// Updated to use global store instead of local state
export const useBusinessProfile = () => {
  const store = useBusinessProfileStore();
  const { toast } = useToast();

  // Load profiles on mount if needed
  useEffect(() => {
    // Only load if we have no profiles, not currently loading, and no recent load attempt
    const shouldLoad = store.profiles.length === 0 &&
                      !store.isLoading &&
                      !store.loadingPromise &&
                      (!store.lastUpdated || Date.now() - store.lastUpdated > 5000); // 5 second cooldown

    if (shouldLoad) {
      console.log('🔍 Loading profiles from useBusinessProfile hook - profiles:', store.profiles.length, 'loading:', store.isLoading, 'promise:', !!store.loadingPromise);
      store.loadProfiles();
    } else {
      console.log('🔍 Skipping profile load - profiles:', store.profiles.length, 'loading:', store.isLoading, 'promise:', !!store.loadingPromise, 'lastUpdated:', store.lastUpdated);
    }
  }, [store.profiles.length, store.isLoading, store.loadingPromise, store.lastUpdated]); // Include loadingPromise

  const switchProfile = async (profileId: string) => {
    try {
      await store.switchProfile(profileId);
      toast({
        title: 'Profile Switched',
        description: 'Business profile switched successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to switch business profile',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const deleteProfile = async (profileId: string) => {
    try {
      await store.deleteProfile(profileId);
      toast({
        title: 'Business Profile Deleted',
        description: 'Business profile has been deleted successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete business profile. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const createProfile = async (profileData: any) => {
    try {
      const newProfile = await store.createProfile(profileData);
      toast({
        title: 'Business Profile Created',
        description: `${newProfile.name} has been created and set as your active profile.`,
      });
      return newProfile;
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create business profile. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const refreshProfiles = () => {
    store.refreshProfiles();
  };

  return {
    profiles: Array.isArray(store.profiles) ? store.profiles : [],
    activeProfile: store.activeProfile,
    profileWithDataSources: store.profileWithDataSources,
    isLoading: store.isLoading,
    switchProfile,
    deleteProfile,
    createProfile,
    refreshProfiles,
    loadProfiles: store.loadProfiles,
  };
};
