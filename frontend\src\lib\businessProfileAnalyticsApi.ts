/**
 * Business Profile Analytics API client
 * 
 * This module provides API functions for business profile analytics,
 * including performance metrics, data source utilization, and ROI measurement.
 */

import { apiRequest } from './api';

// Types
export interface ProfilePerformanceMetrics {
  profile_id: string;
  profile_name: string;
  creation_date: string;
  last_updated: string;
  
  // Usage metrics
  total_conversations: number;
  total_messages: number;
  active_days: number;
  avg_messages_per_conversation: number;
  
  // Data source metrics
  total_data_sources: number;
  active_data_sources: number;
  data_source_utilization_rate: number;
  
  // Engagement metrics
  agent_interactions: Record<string, number>;
  most_used_agent?: string;
  engagement_score: number;
  
  // Performance indicators
  response_quality_score: number;
  user_satisfaction_score: number;
  business_value_score: number;
}

export interface DataSourceUtilization {
  data_source_id: string;
  data_source_name: string;
  data_source_type: string;
  
  // Usage statistics
  total_queries: number;
  successful_queries: number;
  failed_queries: number;
  success_rate: number;
  
  // Performance metrics
  avg_response_time: number;
  total_data_processed: number;
  last_accessed?: string;
  
  // Business impact
  conversations_enhanced: number;
  insights_generated: number;
  value_score: number;
}

export interface AgentEngagement {
  agent_name: string;
  agent_type: string;
  
  // Usage metrics
  total_interactions: number;
  unique_profiles_served: number;
  avg_interactions_per_profile: number;
  
  // Performance metrics
  avg_response_time: number;
  success_rate: number;
  user_satisfaction: number;
  
  // Business impact
  insights_provided: number;
  recommendations_made: number;
  business_value_generated: number;
}

export interface ROIMeasurement {
  profile_id: string;
  
  // Investment metrics
  setup_time_invested: number;
  data_preparation_time: number;
  maintenance_time: number;
  total_time_investment: number;
  
  // Value metrics
  time_saved_per_query: number;
  total_queries_processed: number;
  total_time_saved: number;
  
  // Business impact
  insights_generated: number;
  decisions_supported: number;
  estimated_business_value: number;
  
  // ROI calculation
  roi_percentage: number;
  payback_period_days: number;
}

export interface AnalyticsSummary {
  total_profiles: number;
  active_profiles: number;
  total_conversations: number;
  total_data_sources: number;
  average_engagement_score: number;
  total_roi_percentage: number;
  recommendations: string[];
}

/**
 * Get profile performance metrics
 */
export const getProfilePerformanceMetrics = async (
  profileId: string,
  daysBack: number = 30
): Promise<ProfilePerformanceMetrics> => {
  const params = new URLSearchParams({
    days_back: daysBack.toString(),
  });

  return apiRequest(`/business-profile-analytics/profile/${profileId}/performance?${params}`, {
    method: 'GET',
  });
};

/**
 * Get data source utilization metrics
 */
export const getDataSourceUtilizationMetrics = async (
  profileId: string,
  daysBack: number = 30
): Promise<DataSourceUtilization[]> => {
  const params = new URLSearchParams({
    days_back: daysBack.toString(),
  });

  return apiRequest(`/business-profile-analytics/profile/${profileId}/data-source-utilization?${params}`, {
    method: 'GET',
  });
};

/**
 * Get agent engagement metrics
 */
export const getAgentEngagementMetrics = async (
  daysBack: number = 30
): Promise<AgentEngagement[]> => {
  const params = new URLSearchParams({
    days_back: daysBack.toString(),
  });

  return apiRequest(`/business-profile-analytics/agent-engagement?${params}`, {
    method: 'GET',
  });
};

/**
 * Get ROI measurement metrics
 */
export const getROIMeasurementMetrics = async (
  profileId: string
): Promise<ROIMeasurement> => {
  return apiRequest(`/business-profile-analytics/profile/${profileId}/roi`, {
    method: 'GET',
  });
};

/**
 * Get analytics summary
 */
export const getAnalyticsSummary = async (
  daysBack: number = 30
): Promise<AnalyticsSummary> => {
  const params = new URLSearchParams({
    days_back: daysBack.toString(),
  });

  return apiRequest(`/business-profile-analytics/summary?${params}`, {
    method: 'GET',
  });
};

/**
 * Business Profile Analytics API client object
 */
export const businessProfileAnalyticsApi = {
  getProfilePerformanceMetrics,
  getDataSourceUtilizationMetrics,
  getAgentEngagementMetrics,
  getROIMeasurementMetrics,
  getAnalyticsSummary,
};

/**
 * Hook for using business profile analytics
 */
export const useBusinessProfileAnalytics = () => {
  return {
    getProfilePerformanceMetrics,
    getDataSourceUtilizationMetrics,
    getAgentEngagementMetrics,
    getROIMeasurementMetrics,
    getAnalyticsSummary,
  };
};

/**
 * Utility functions for working with analytics data
 */
export const analyticsUtils = {
  /**
   * Format engagement score color
   */
  getEngagementScoreColor: (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
  },

  /**
   * Format ROI percentage color
   */
  getROIColor: (percentage: number): string => {
    if (percentage >= 200) return 'text-green-600';
    if (percentage >= 100) return 'text-blue-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-red-600';
  },

  /**
   * Format success rate color
   */
  getSuccessRateColor: (rate: number): string => {
    if (rate >= 0.9) return 'text-green-600';
    if (rate >= 0.8) return 'text-blue-600';
    if (rate >= 0.7) return 'text-yellow-600';
    return 'text-red-600';
  },

  /**
   * Format time duration
   */
  formatDuration: (hours: number): string => {
    if (hours < 1) {
      return `${Math.round(hours * 60)} minutes`;
    } else if (hours < 24) {
      return `${hours.toFixed(1)} hours`;
    } else {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      return `${days} days${remainingHours > 0 ? ` ${remainingHours.toFixed(1)} hours` : ''}`;
    }
  },

  /**
   * Format currency value
   */
  formatCurrency: (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  },

  /**
   * Format percentage
   */
  formatPercentage: (value: number, decimals: number = 1): string => {
    return `${value.toFixed(decimals)}%`;
  },

  /**
   * Get engagement level description
   */
  getEngagementLevel: (score: number): string => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    if (score >= 20) return 'Poor';
    return 'Very Low';
  },

  /**
   * Get ROI description
   */
  getROIDescription: (percentage: number): string => {
    if (percentage >= 300) return 'Exceptional ROI';
    if (percentage >= 200) return 'Excellent ROI';
    if (percentage >= 100) return 'Good ROI';
    if (percentage >= 50) return 'Moderate ROI';
    if (percentage >= 0) return 'Break-even';
    return 'Negative ROI';
  },

  /**
   * Calculate trend from historical data
   */
  calculateTrend: (current: number, previous: number): {
    percentage: number;
    direction: 'up' | 'down' | 'stable';
    color: string;
  } => {
    if (previous === 0) {
      return { percentage: 0, direction: 'stable', color: 'text-gray-600' };
    }

    const percentage = ((current - previous) / previous) * 100;
    
    if (Math.abs(percentage) < 5) {
      return { percentage, direction: 'stable', color: 'text-gray-600' };
    }
    
    return {
      percentage,
      direction: percentage > 0 ? 'up' : 'down',
      color: percentage > 0 ? 'text-green-600' : 'text-red-600',
    };
  },

  /**
   * Get top performing data sources
   */
  getTopDataSources: (
    utilizations: DataSourceUtilization[], 
    limit: number = 5
  ): DataSourceUtilization[] => {
    return [...utilizations]
      .sort((a, b) => b.value_score - a.value_score)
      .slice(0, limit);
  },

  /**
   * Get most engaged agents
   */
  getMostEngagedAgents: (
    engagements: AgentEngagement[], 
    limit: number = 5
  ): AgentEngagement[] => {
    return [...engagements]
      .sort((a, b) => b.total_interactions - a.total_interactions)
      .slice(0, limit);
  },

  /**
   * Calculate average metrics
   */
  calculateAverages: (metrics: ProfilePerformanceMetrics[]): {
    avgEngagementScore: number;
    avgUtilizationRate: number;
    avgBusinessValue: number;
  } => {
    if (metrics.length === 0) {
      return { avgEngagementScore: 0, avgUtilizationRate: 0, avgBusinessValue: 0 };
    }

    const totals = metrics.reduce(
      (acc, metric) => ({
        engagement: acc.engagement + metric.engagement_score,
        utilization: acc.utilization + metric.data_source_utilization_rate,
        businessValue: acc.businessValue + metric.business_value_score,
      }),
      { engagement: 0, utilization: 0, businessValue: 0 }
    );

    return {
      avgEngagementScore: totals.engagement / metrics.length,
      avgUtilizationRate: totals.utilization / metrics.length,
      avgBusinessValue: totals.businessValue / metrics.length,
    };
  },
};
