import { useState, useEffect, useRef, useCallback } from "react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Database, FileUp, MessageSquare, RefreshCw, Upload, Loader2, AlertCircle, CheckCircle2, Trash2, Edit, Eye, Plus, Search, Filter, MoreVertical, Download, Building2, Target, Briefcase, Settings } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { dataSourceApi, DataSource, FileDataSourceCreate, DatabaseDataSourceCreate, ApiDataSourceCreate, McpDataSourceCreate } from "@/lib/dataSourceApi";
import { MCPServerDashboard } from "@/components/mcp/MCPServerDashboard";
import { MCPServerConfig } from "@/components/mcp/MCPServerConfig";
import { businessProfileApi, BusinessProfile, BusinessProfileCreate, BusinessProfileWithDataSources } from "@/lib/businessProfileApi";
import { fileApi } from "@/lib/api";
import { Progress } from "@/components/ui/progress";
import { useAutoRefreshOnUpload, useDataSourceRefresh } from "@/hooks/use-data-source-refresh";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useDataIntegrationState } from "@/hooks/use-page-state";
import { BusinessProfileForm } from "@/components/business-profile/BusinessProfileForm";
import { BusinessProfileSelector, useBusinessProfile } from "@/components/business-profile/BusinessProfileSelector";
import { DataSourceAssignment } from "@/components/business-profile/DataSourceAssignment";
import { EditProfileModal } from "@/components/business-profile/EditProfileModal";
import { DefaultProfileNotification } from "@/components/business-profile/DefaultProfileNotification";
import { ProfileAnalytics } from "@/components/business-profile/ProfileAnalytics";
import { ProfileComparison } from "@/components/business-profile/ProfileComparison";
import { BusinessProfileDebug } from "@/components/debug/BusinessProfileDebug";
import { useDataManager } from "@/hooks/use-data-manager";
import { useSmartRefresh } from "@/hooks/use-smart-refresh";

const DataIntegration = () => {
  // Page state management
  const { state: pageState, setState: setPageState } = useDataIntegrationState();

  // Local UI state for business profiles
  const [isCreatingProfile, setIsCreatingProfile] = useState(false);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [isProfileDeleteDialogOpen, setIsProfileDeleteDialogOpen] = useState(false);
  const [isDeletingProfile, setIsDeletingProfile] = useState(false);
  const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState(false);

  // Integration wizard state - initialize from page state
  const [selectedOption, setSelectedOption] = useState<string | null>(
    pageState?.formData?.selectedOption || null
  );
  const [currentStep, setCurrentStep] = useState(pageState?.formData?.currentStep || 1);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState<boolean>(false);
  const [selectedFileName, setSelectedFileName] = useState<string>(
    pageState?.formData?.selectedFileName || ""
  );

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Data management state
  const [activeTab, setActiveTab] = useState<"profiles" | "wizard" | "manage">(
    pageState?.activeTab || "profiles"
  );
  const [filteredDataSources, setFilteredDataSources] = useState<DataSource[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editFormData, setEditFormData] = useState<any>({});
  const [isFileDeleteWarningOpen, setIsFileDeleteWarningOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<{ id: string; name: string } | null>(null);
  const [relatedDatasources, setRelatedDatasources] = useState<any[]>([]);

  // MCP Server management state
  const [showMCPConfig, setShowMCPConfig] = useState(false);
  const [showMCPDashboard, setShowMCPDashboard] = useState(false);
  const [mcpEditServer, setMcpEditServer] = useState<any>(null);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    fileId: "",
    dbType: "postgresql",
    host: "",
    port: 5432,
    database: "",
    username: "",
    password: "",
    apiType: "rest",
    endpoint: "",
    authType: "api_key",
    authCredentials: "",
    namespace: "default",
    collectionIds: []
  });
  const [connectionStatus, setConnectionStatus] = useState<"idle" | "testing" | "success" | "error">("idle");
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [connectionSummary, setConnectionSummary] = useState<any>(null);

  const navigate = useNavigate();
  const { toast } = useToast();
  const { handleFileUploaded, handleDataSourceCreated } = useAutoRefreshOnUpload();
  const { refreshTrigger } = useDataSourceRefresh();

  // Centralized data management
  const {
    dataSources,
    files,
    isLoading: isLoadingData,
    isRefreshing: isAutoRefreshing,
    error: dataError,
    hasErrors,
    isCircuitOpen,
    refresh,
    forceRefresh,
  } = useDataManager();

  // Smart auto-refresh coordination
  const { manualRefresh } = useSmartRefresh({
    onRefresh: (source: string) => {
      refresh({ source, showLoading: source === 'manual' });
    },
    isEnabled: !isCircuitOpen, // Disable when circuit breaker is open
    activeTab,
    refreshTrigger,
  });

  // Use shared business profile hook
  const {
    profiles,
    activeProfile,
    profileWithDataSources,
    isLoading: isLoadingProfiles,
    createProfile,
    deleteProfile,
    refreshProfiles
  } = useBusinessProfile();

  // Business Profile Functions

  const handleCreateProfile = async (profileData: BusinessProfileCreate) => {
    try {
      setIsCreatingProfile(true);
      await createProfile(profileData);

      // Hide form and show success
      setShowProfileForm(false);
      setActiveTab("profiles");
    } catch (error) {
      console.error('Error creating business profile:', error);
      // Error handling is done in the shared hook
    } finally {
      setIsCreatingProfile(false);
    }
  };

  const handleDeleteProfile = async () => {
    if (!activeProfile) return;

    try {
      setIsDeletingProfile(true);
      await deleteProfile(activeProfile.id);
      setIsProfileDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting business profile:', error);
      // Error handling is done in the shared hook
    } finally {
      setIsDeletingProfile(false);
    }
  };

  const handleProfileChange = async (profile: BusinessProfile | null) => {
    // Refresh data sources when profile changes
    if (activeTab === "manage") {
      refresh({ source: 'profile-change' });
    }
  };

  const handleDataSourceAssignmentsChange = (assignments: any[]) => {
    // Refresh profiles to get updated data source assignments
    refreshProfiles();
  };



  // Save page state when key values change (with debouncing)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setPageState({
        activeTab,
        formData: {
          selectedOption,
          currentStep,
          selectedFileName,
        },
      });
    }, 100); // Debounce to prevent infinite loops

    return () => clearTimeout(timeoutId);
  }, [activeTab, selectedOption, currentStep, selectedFileName, setPageState]); // Include setPageState for completeness



  // Load files when user navigates to file selection step
  useEffect(() => {
    if (selectedOption === "file-upload" && currentStep === 2) {
      // For user navigation, we can trigger a manual refresh
      refresh({ showLoading: true, source: 'file-selection' });
    }
  }, [selectedOption, currentStep, refresh]);

  // Filter data sources based on search and type
  useEffect(() => {
    let filtered = dataSources;

    if (searchQuery) {
      filtered = filtered.filter(ds =>
        ds.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ds.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (typeFilter !== "all") {
      filtered = filtered.filter(ds => ds.type === typeFilter);
    }

    setFilteredDataSources(filtered);
  }, [dataSources, searchQuery, typeFilter]);

  const integrationOptions = [
    {
      id: "file-upload",
      title: "File Upload",
      description: "Upload CSV, Excel, DOC, DOCX, or PDF files",
      icon: FileUp,
    },
    {
      id: "database",
      title: "Database Connection",
      description: "Connect to your existing database",
      icon: Database,
    },
    {
      id: "api",
      title: "API Integration",
      description: "Connect through REST or GraphQL APIs",
      icon: RefreshCw,
    },
    {
      id: "mcp",
      title: "MCP Server",
      description: "Connect to Model Context Protocol servers",
      icon: MessageSquare,
    },
    {
      id: "mcp-enhanced",
      title: "Enhanced MCP Configuration",
      description: "Advanced MCP server setup with JSON configuration support",
      icon: Settings,
    },
  ];

  const handleNext = async () => {
    if (currentStep < 3) {
      // Validate form data before proceeding
      if (currentStep === 2) {
        if (!validateForm()) {
          return;
        }

        // Test connection before proceeding to step 3
        await testConnection();
      } else {
        setCurrentStep(currentStep + 1);
      }
    } else {
      // Navigate to data chat when integration is complete
      navigate("/data-chat");
    }
  };

  // Handle navigation when clicking on step numbers
  const handleStepClick = async (step: number) => {
    // Don't do anything if clicking the current step
    if (step === currentStep) return;

    // Allow going back to previous steps freely
    if (step < currentStep) {
      setCurrentStep(step);
      return;
    }

    // For moving forward, apply validation
    if (step > currentStep) {
      // Can't skip from step 1 to 3
      if (currentStep === 1 && step === 3) {
        toast({
          title: "Navigation Error",
          description: "Please configure your data source before validating.",
          variant: "destructive",
        });
        return;
      }

      // Moving from step 1 to 2 requires a selection
      if (currentStep === 1 && step === 2) {
        if (!selectedOption) {
          toast({
            title: "Selection Required",
            description: "Please select a data source type first.",
            variant: "destructive",
          });
          return;
        }
        setCurrentStep(2);
        return;
      }

      // Moving from step 2 to 3 requires validation and connection test
      if (currentStep === 2 && step === 3) {
        if (!validateForm()) {
          return;
        }
        await testConnection();
        // Note: testConnection will set currentStep to 3 if successful
      }
    }
  };

  const validateForm = () => {
    // Basic form validation
    if (!formData.name) {
      toast({
        title: "Validation Error",
        description: "Please enter a name for your data source.",
        variant: "destructive",
      });
      return false;
    }

    if (selectedOption === "file-upload" && !formData.fileId) {
      toast({
        title: "Validation Error",
        description: "Please select a file.",
        variant: "destructive",
      });
      return false;
    }

    if (selectedOption === "database") {
      if (!formData.host || !formData.database || !formData.username || !formData.password) {
        toast({
          title: "Validation Error",
          description: "Please fill in all database connection fields.",
          variant: "destructive",
        });
        return false;
      }
    }

    if (selectedOption === "api") {
      if (!formData.endpoint) {
        toast({
          title: "Validation Error",
          description: "Please enter an API endpoint URL.",
          variant: "destructive",
        });
        return false;
      }
    }

    if (selectedOption === "mcp") {
      if (!formData.endpoint) {
        toast({
          title: "Validation Error",
          description: "Please enter an MCP server endpoint URL.",
          variant: "destructive",
        });
        return false;
      }
    }

    return true;
  };

  const testConnection = async () => {
    setConnectionStatus("testing");
    setIsLoading(true);
    setConnectionError(null);

    try {
      // Create data source based on selected option
      let response;

      if (selectedOption === "file-upload") {
        const fileDataSource: FileDataSourceCreate = {
          name: formData.name,
          type: "file",
          description: formData.description,
          file_id: formData.fileId
        };

        response = await dataSourceApi.createFileDataSource(fileDataSource);
      } else if (selectedOption === "database") {
        const dbDataSource: DatabaseDataSourceCreate = {
          name: formData.name,
          type: "database",
          description: formData.description,
          db_type: formData.dbType,
          host: formData.host,
          port: formData.port,
          database: formData.database,
          username: formData.username,
          password: formData.password
        };

        response = await dataSourceApi.createDatabaseDataSource(dbDataSource);
      } else if (selectedOption === "api") {
        const apiDataSource: ApiDataSourceCreate = {
          name: formData.name,
          type: "api",
          description: formData.description,
          api_type: formData.apiType,
          endpoint: formData.endpoint,
          auth_type: formData.authType,
          auth_credentials: formData.authCredentials ? { key: formData.authCredentials } : undefined
        };

        response = await dataSourceApi.createApiDataSource(apiDataSource);
      } else if (selectedOption === "mcp") {
        const mcpDataSource: McpDataSourceCreate = {
          name: formData.name,
          type: "mcp",
          description: formData.description,
          endpoint: formData.endpoint,
          api_key: formData.authCredentials || undefined,
          namespace: formData.namespace,
          collection_ids: []
        };

        response = await dataSourceApi.createMcpDataSource(mcpDataSource);
      }

      // Trigger data source refresh
      if (response) {
        handleDataSourceCreated(response);
      }

      // Set connection summary
      setConnectionSummary({
        id: response?.id,
        type: selectedOption === "file-upload" ? "File Upload" :
              selectedOption === "database" ? "Database Connection" :
              selectedOption === "api" ? "API Integration" : "MCP Server",
        name: formData.name,
        description: formData.description
      });

      setConnectionStatus("success");
      setCurrentStep(3);
    } catch (error) {
      console.error("Connection test failed:", error);
      setConnectionStatus("error");
      setConnectionError(error instanceof Error ? error.message : "Unknown error occurred");

      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : "Failed to connect to data source. Please check your settings and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Update the selected file name for display
    setSelectedFileName(file.name);

    // Reset previous upload state
    setUploadError(null);
    setUploadSuccess(false);
    setUploadProgress(0);
    setIsUploading(true);

    // Create a reference to store the interval ID
    let progressInterval: NodeJS.Timeout;

    try {
      // Simulate upload progress
      progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      // Upload the file
      const response = await fileApi.uploadFile(file);

      // Trigger data source refresh
      handleFileUploaded(response);

      // Clear the progress interval
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Refresh data to include the new file and select it
      refresh({ source: 'file-upload' });
      handleInputChange("fileId", response.id);

      // Show success message
      toast({
        title: "File Uploaded",
        description: `${file.name} has been uploaded successfully.`,
      });

      // Set upload success state
      setUploadSuccess(true);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
        setSelectedFileName("");
      }
    } catch (error) {
      console.error("Error uploading file:", error);

      // Handle authentication errors specifically
      if (error instanceof Error && error.message.includes('Authentication')) {
        setUploadError("Authentication failed. Please refresh the page and log in again.");

        // Show a toast for authentication errors
        toast({
          title: "Authentication Error",
          description: "Your session has expired. Please refresh the page and log in again.",
          variant: "destructive",
        });
      } else {
        setUploadError(error instanceof Error ? error.message : "Failed to upload file. Please try again.");
      }
    } finally {
      setIsUploading(false);
      // Clear the progress interval if it's still running
      clearInterval(progressInterval);
    }
  };

  // CRUD Functions for Data Management
  const handleDeleteDataSource = async (dataSource: DataSource) => {
    try {
      await dataSourceApi.deleteDataSource(dataSource.id);
      toast({
        title: "Success",
        description: `Data source "${dataSource.name}" has been deleted.`,
      });
      refresh({ showLoading: true, source: 'datasource-delete' }); // Refresh data after deletion
      setIsDeleteDialogOpen(false);
      setSelectedDataSource(null);
    } catch (error) {
      console.error("Error deleting data source:", error);
      toast({
        title: "Error",
        description: "Failed to delete data source. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteFile = async (fileId: string, fileName: string) => {
    try {
      // First check if the file has related datasources
      const relatedResponse = await fileApi.getFileRelatedDatasources(fileId);

      if (relatedResponse.related_datasources.length > 0) {
        // Show warning modal if there are related datasources
        setFileToDelete({ id: fileId, name: fileName });
        setRelatedDatasources(relatedResponse.related_datasources);
        setIsFileDeleteWarningOpen(true);
      } else {
        // No related datasources, delete directly
        await fileApi.deleteFile(fileId, false);
        toast({
          title: "Success",
          description: `File "${fileName}" has been deleted.`,
        });
        refresh({ showLoading: true, source: 'file-delete' }); // Refresh data after file deletion
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      toast({
        title: "Error",
        description: "Failed to delete file. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleConfirmFileDelete = async (deleteRelatedDatasources: boolean) => {
    if (!fileToDelete) return;

    try {
      await fileApi.deleteFile(fileToDelete.id, deleteRelatedDatasources);
      toast({
        title: "Success",
        description: deleteRelatedDatasources
          ? `File "${fileToDelete.name}" and its related data sources have been deleted.`
          : `File "${fileToDelete.name}" has been deleted.`,
      });
      refresh({ showLoading: true, source: 'file-delete-with-datasources' }); // Refresh data after file deletion
      setIsFileDeleteWarningOpen(false);
      setFileToDelete(null);
      setRelatedDatasources([]);
    } catch (error) {
      console.error("Error deleting file:", error);
      toast({
        title: "Error",
        description: "Failed to delete file. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleViewDataSource = (dataSource: DataSource) => {
    setSelectedDataSource(dataSource);
    setIsViewDialogOpen(true);
  };

  const handleEditDataSource = (dataSource: DataSource) => {
    setSelectedDataSource(dataSource);
    setEditFormData({
      name: dataSource.name,
      description: dataSource.description || "",
      is_active: dataSource.is_active
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateDataSource = async () => {
    if (!selectedDataSource) return;

    try {
      await dataSourceApi.updateDataSource(selectedDataSource.id, editFormData);
      toast({
        title: "Success",
        description: `Data source "${editFormData.name}" has been updated.`,
      });
      refresh({ showLoading: true, source: 'datasource-edit' }); // Refresh data after edit
      setIsEditDialogOpen(false);
      setSelectedDataSource(null);
      setEditFormData({});
    } catch (error) {
      console.error("Error updating data source:", error);
      toast({
        title: "Error",
        description: "Failed to update data source. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getDataSourceIcon = (type: string) => {
    switch (type) {
      case "file": return FileUp;
      case "database": return Database;
      case "api": return RefreshCw;
      case "mcp": return MessageSquare;
      default: return Database;
    }
  };

  const getDataSourceTypeLabel = (type: string) => {
    switch (type) {
      case "file": return "File Upload";
      case "database": return "Database";
      case "api": return "API";
      case "mcp": return "MCP Server";
      default: return type;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6 max-w-6xl mx-auto"
      >
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <Building2 className="h-6 w-6" />
                Business Profile Setup
              </h1>
              <p className="text-muted-foreground">
                Create business profiles and manage data sources to provide context for AI agents
              </p>
            </div>
            {isAutoRefreshing && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Auto-refreshing...</span>
              </div>
            )}
          </div>

          {/* Business Profile Selector */}
          <div className="flex items-center justify-between">
            <BusinessProfileSelector
              onProfileChange={handleProfileChange}
              onCreateNew={() => setShowProfileForm(true)}
            />
            {activeProfile && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Target className="h-4 w-4" />
                <span>Active Profile</span>
              </div>
            )}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "profiles" | "wizard" | "manage" | "analytics" | "mcp")}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="profiles">Business Profile</TabsTrigger>
            <TabsTrigger value="manage">Data Sources</TabsTrigger>
            <TabsTrigger value="wizard">Add New Source</TabsTrigger>
            <TabsTrigger value="mcp">MCP Servers</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="profiles" className="space-y-6">
            {/* Business Profile Management */}
            {showProfileForm ? (
              <BusinessProfileForm
                onSubmit={handleCreateProfile}
                onCancel={() => setShowProfileForm(false)}
                isLoading={isCreatingProfile}
                submitLabel="Create Business Profile"
                title="Create Business Profile"
                description="Set up your business profile to provide context for AI agents"
              />
            ) : activeProfile ? (
              <div className="space-y-6">
                {/* Default Profile Notification */}
                <DefaultProfileNotification
                  profile={activeProfile}
                  onCustomizeProfile={() => setIsEditProfileModalOpen(true)}
                  onCreateNewProfile={() => setShowProfileForm(true)}
                />

                {/* Active Profile Overview */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <Building2 className="h-5 w-5" />
                          {activeProfile.name}
                        </CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          {activeProfile.business_type && (
                            <Badge variant="secondary">{activeProfile.business_type}</Badge>
                          )}
                          {activeProfile.industry && (
                            <Badge variant="outline">{activeProfile.industry}</Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsEditProfileModalOpen(true)}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Profile
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsProfileDeleteDialogOpen(true)}
                          className="text-destructive hover:text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Profile
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {activeProfile.target_audience && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Target Audience</h4>
                          <p className="text-sm">{activeProfile.target_audience}</p>
                        </div>
                      )}
                      {activeProfile.products_services && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Products & Services</h4>
                          <p className="text-sm">{activeProfile.products_services}</p>
                        </div>
                      )}
                      {activeProfile.marketing_goals && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Marketing Goals</h4>
                          <p className="text-sm">{activeProfile.marketing_goals}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Data Source Assignment */}
                {profileWithDataSources && (
                  <DataSourceAssignment
                    profileId={activeProfile.id}
                    assignments={profileWithDataSources.data_source_assignments}
                    availableDataSources={dataSources}
                    onAssignmentsChange={handleDataSourceAssignmentsChange}
                  />
                )}
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center h-64 text-center">
                  <Building2 className="h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Business Profile</h3>
                  <p className="text-gray-500 mb-4">
                    Create a business profile to provide context for AI agents and organize your data sources.
                  </p>
                  <Button onClick={() => setShowProfileForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Business Profile
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="manage" className="space-y-6">
            {/* Data Management Interface */}
            <div className="space-y-4">
              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search data sources..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[180px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="file">File Upload</SelectItem>
                    <SelectItem value="database">Database</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                    <SelectItem value="mcp">MCP Server</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  onClick={manualRefresh}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh
                </Button>
                <Button
                  onClick={() => setActiveTab("wizard")}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Data Source
                </Button>
              </div>

              {/* Error Status Indicators */}
              {isCircuitOpen && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <AlertCircle className="h-5 w-5 text-red-600" />
                    <div>
                      <p className="text-sm font-medium text-red-800">Auto-refresh temporarily disabled</p>
                      <p className="text-xs text-red-600">
                        Multiple errors detected. Auto-refresh will resume automatically in 1 minute.
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={forceRefresh}
                    className="text-red-700 border-red-300 hover:bg-red-100"
                  >
                    Try Now
                  </Button>
                </div>
              )}

              {dataError && !isCircuitOpen && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800">Data loading issue</p>
                      <p className="text-xs text-yellow-600">
                        {dataError}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={forceRefresh}
                    className="text-yellow-700 border-yellow-300 hover:bg-yellow-100"
                  >
                    Retry
                  </Button>
                </div>
              )}

              {/* Data Sources Grid */}
              {isLoadingData ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : filteredDataSources.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center h-64 text-center">
                    <Database className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Data Sources Found</h3>
                    <p className="text-gray-500 mb-4">
                      {searchQuery || typeFilter !== "all"
                        ? "No data sources match your current filters."
                        : "You haven't added any data sources yet."}
                    </p>
                    <Button onClick={() => setActiveTab("wizard")}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Data Source
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredDataSources.map((dataSource) => {
                    const IconComponent = getDataSourceIcon(dataSource.type);
                    return (
                      <Card key={dataSource.id} className="hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                                <IconComponent className="h-5 w-5 text-primary" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="font-medium truncate">{dataSource.name}</h3>
                                <Badge variant="secondary" className="text-xs">
                                  {getDataSourceTypeLabel(dataSource.type)}
                                </Badge>
                              </div>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleViewDataSource(dataSource)}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditDataSource(dataSource)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedDataSource(dataSource);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="space-y-2">
                            {dataSource.description && (
                              <p className="text-sm text-gray-600 line-clamp-2">
                                {dataSource.description}
                              </p>
                            )}
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>Created {formatDate(dataSource.created_at)}</span>
                              <Badge variant={dataSource.is_active ? "default" : "secondary"}>
                                {dataSource.is_active ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}

              {/* Files Section */}
              <div className="mt-8">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileUp className="h-5 w-5" />
                      Uploaded Files
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {files.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <FileUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No files uploaded yet</p>
                        <p className="text-sm">Upload files to create file-based data sources</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {files.map((file) => (
                          <div key={file.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div className="flex items-center space-x-3">
                              <FileUp className="h-5 w-5 text-gray-500" />
                              <div>
                                <p className="font-medium">{file.filename}</p>
                                <p className="text-sm text-gray-500">
                                  {file.num_rows ? `${file.num_rows} rows` : 'Unknown size'} •
                                  {file.file_size ? formatFileSize(file.file_size) : 'Unknown size'} •
                                  {formatDate(file.created_at)}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteFile(file.id, file.filename)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="wizard" className="space-y-6">
            {/* Integration Wizard */}
            <div className="flex justify-between items-center mb-8">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center space-x-8">
              <div className="text-xs text-muted-foreground italic">Click numbers to navigate between steps</div>
            </div>
            <div className="flex items-center space-x-8">
              <div className={`flex flex-col items-center ${currentStep >= 1 ? "text-primary" : "text-muted-foreground"}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2
                    ${currentStep >= 1 ? "border-primary bg-primary/10" : "border-muted"}
                    cursor-pointer hover:shadow-md transition-all hover:scale-110`}
                  onClick={() => handleStepClick(1)}
                  role="button"
                  aria-label="Go to step 1"
                  title="Go to step 1: Select Source"
              >
                1
              </div>
              <span className="text-xs mt-1">Select Source</span>
            </div>
              <div className={`flex flex-col items-center ${currentStep >= 2 ? "text-primary" : "text-muted-foreground"}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2
                    ${currentStep >= 2 ? "border-primary bg-primary/10" : "border-muted"}
                    cursor-pointer hover:shadow-md transition-all hover:scale-110`}
                  onClick={() => handleStepClick(2)}
                  role="button"
                  aria-label="Go to step 2"
                  title="Go to step 2: Configure"
              >
                2
              </div>
              <span className="text-xs mt-1">Configure</span>
            </div>
              <div className={`flex flex-col items-center ${currentStep >= 3 ? "text-primary" : "text-muted-foreground"}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2
                    ${currentStep >= 3 ? "border-primary bg-primary/10" : "border-muted"}
                    cursor-pointer hover:shadow-md transition-all hover:scale-110`}
                  onClick={() => handleStepClick(3)}
                  role="button"
                  aria-label="Go to step 3"
                  title="Go to step 3: Validate"
              >
                3
              </div>
              <span className="text-xs mt-1">Validate</span>
            </div>
            </div>
          </div>
        </div>

        {currentStep === 1 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            <h2 className="text-lg font-medium">Choose a data source</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {integrationOptions.map((option) => (
                <Card
                  key={option.id}
                  className={`cursor-pointer transition-all ${
                    selectedOption === option.id
                      ? "border-primary ring-1 ring-primary"
                      : "hover:border-primary/50"
                  }`}
                  onClick={() => {
                    if (option.id === "mcp-enhanced") {
                      setShowMCPConfig(true);
                    } else {
                      setSelectedOption(option.id);
                      setCurrentStep(2);
                    }
                  }}
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center space-y-3">
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <option.icon className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="font-medium">{option.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {option.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        )}

        {currentStep === 2 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            <h2 className="text-lg font-medium">Configure your data source</h2>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    Configure the connection details for your selected data source.
                  </p>

                  {selectedOption === "file-upload" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My CSV Data"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Select File</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.fileId}
                          onChange={(e) => handleInputChange("fileId", e.target.value)}
                        >
                          <option value="">Select a file...</option>
                          {files.map(file => (
                            <option key={file.id} value={file.id}>
                              {file.filename} ({file.num_rows || "unknown"} rows)
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm mb-1">Or Upload a New File</label>
                        <div className="flex items-center gap-2">
                          <input
                            type="file"
                            ref={fileInputRef}
                            className="hidden"
                            accept=".csv,.xlsx,.xls,.doc,.docx,.pdf"
                            onChange={handleFileChange}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                            disabled={isUploading}
                            className="flex items-center gap-2"
                          >
                            <Upload className="h-4 w-4" />
                            Choose File
                          </Button>
                          <span className="text-sm text-muted-foreground">
                            {selectedFileName || "No file selected"}
                          </span>
                          {isUploading && (
                            <Loader2 className="h-4 w-4 animate-spin ml-2" />
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Supported formats: CSV, Excel, DOC, DOCX, PDF
                        </div>

                        {isUploading && (
                          <div className="mt-2">
                            <Progress value={uploadProgress} className="h-2" />
                            <p className="text-xs text-center mt-1">{uploadProgress}% Uploaded</p>
                          </div>
                        )}

                        {uploadError && (
                          <Alert variant="destructive" className="mt-2">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Upload Failed</AlertTitle>
                            <AlertDescription>{uploadError}</AlertDescription>
                          </Alert>
                        )}

                        {uploadSuccess && (
                          <Alert variant="success" className="mt-2 bg-green-50 border-green-200 text-green-800">
                            <CheckCircle2 className="h-4 w-4 text-green-600" />
                            <AlertTitle>Upload Successful</AlertTitle>
                            <AlertDescription>File has been uploaded and selected.</AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </div>
                  )}

                  {selectedOption === "database" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My Database"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Database Type</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.dbType}
                          onChange={(e) => handleInputChange("dbType", e.target.value)}
                        >
                          <option value="postgresql">PostgreSQL</option>
                          <option value="mysql">MySQL</option>
                          <option value="sqlserver">SQL Server</option>
                          <option value="mongodb">MongoDB</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Host</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="host.example.com"
                          value={formData.host}
                          onChange={(e) => handleInputChange("host", e.target.value)}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm mb-1">Port</label>
                          <input
                            type="number"
                            className="w-full p-2 border rounded"
                            placeholder="5432"
                            value={formData.port}
                            onChange={(e) => handleInputChange("port", parseInt(e.target.value))}
                          />
                        </div>
                        <div>
                          <label className="block text-sm mb-1">Database Name</label>
                          <input
                            type="text"
                            className="w-full p-2 border rounded"
                            placeholder="mydb"
                            value={formData.database}
                            onChange={(e) => handleInputChange("database", e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm mb-1">Username</label>
                          <input
                            type="text"
                            className="w-full p-2 border rounded"
                            placeholder="username"
                            value={formData.username}
                            onChange={(e) => handleInputChange("username", e.target.value)}
                          />
                        </div>
                        <div>
                          <label className="block text-sm mb-1">Password</label>
                          <input
                            type="password"
                            className="w-full p-2 border rounded"
                            placeholder="********"
                            value={formData.password}
                            onChange={(e) => handleInputChange("password", e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedOption === "mcp" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My MCP Server"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">MCP Server Endpoint</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="https://mcp.example.com"
                          value={formData.endpoint}
                          onChange={(e) => handleInputChange("endpoint", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Key (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Enter API key if required"
                          value={formData.authCredentials}
                          onChange={(e) => handleInputChange("authCredentials", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Namespace</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="default"
                          value={formData.namespace}
                          onChange={(e) => handleInputChange("namespace", e.target.value)}
                        />
                      </div>
                    </div>
                  )}

                  {selectedOption === "api" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm mb-1">Data Source Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="My API"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Description (Optional)</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Description of this data source"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Type</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.apiType}
                          onChange={(e) => handleInputChange("apiType", e.target.value)}
                        >
                          <option value="rest">REST</option>
                          <option value="graphql">GraphQL</option>
                          <option value="soap">SOAP</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Endpoint URL</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="https://api.example.com/v1/data"
                          value={formData.endpoint}
                          onChange={(e) => handleInputChange("endpoint", e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Authentication Method</label>
                        <select
                          className="w-full p-2 border rounded"
                          value={formData.authType}
                          onChange={(e) => handleInputChange("authType", e.target.value)}
                        >
                          <option value="api_key">API Key</option>
                          <option value="oauth">OAuth 2.0</option>
                          <option value="basic">Basic Auth</option>
                          <option value="bearer">Bearer Token</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">API Key / Token</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded"
                          placeholder="Enter API key or token"
                          value={formData.authCredentials}
                          onChange={(e) => handleInputChange("authCredentials", e.target.value)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {currentStep === 3 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            <h2 className="text-lg font-medium">Validate Connection</h2>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-center p-8">
                    <div className="text-center">
                      {connectionStatus === "success" ? (
                        <>
                          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium mb-2">Connection Successful!</h3>
                          <p className="text-muted-foreground mb-4">
                            Your data source has been connected successfully. You're ready to start analyzing your data.
                          </p>
                        </>
                      ) : connectionStatus === "error" ? (
                        <>
                          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </div>
                          <h3 className="text-lg font-medium mb-2">Connection Failed</h3>
                          <p className="text-muted-foreground mb-4">
                            {connectionError || "Failed to connect to the data source. Please check your settings and try again."}
                          </p>
                        </>
                      ) : (
                        <>
                          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
                          </div>
                          <h3 className="text-lg font-medium mb-2">Testing Connection...</h3>
                          <p className="text-muted-foreground mb-4">
                            Please wait while we test the connection to your data source.
                          </p>
                        </>
                      )}
                    </div>
                  </div>

                  {connectionStatus === "success" && connectionSummary && (
                    <div className="bg-muted/30 rounded-lg p-4">
                      <h4 className="font-medium mb-2">Connection Summary</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="text-muted-foreground">Source Type:</div>
                        <div>{connectionSummary.type}</div>

                        <div className="text-muted-foreground">Name:</div>
                        <div>{connectionSummary.name}</div>

                        {connectionSummary.description && (
                          <>
                            <div className="text-muted-foreground">Description:</div>
                            <div>{connectionSummary.description}</div>
                          </>
                        )}

                        <div className="text-muted-foreground">ID:</div>
                        <div className="truncate">{connectionSummary.id}</div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStep(2)}
                    >
                      Configure Again
                    </Button>
                    <div className="flex items-center gap-2">
                      <div className="text-muted-foreground text-sm">
                        Data ready for analysis
                      </div>
                      <MessageSquare className="h-4 w-4 text-primary" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

            <div className="flex justify-end mt-6">
              <Button
                onClick={handleNext}
                disabled={(currentStep === 1 && !selectedOption) || isLoading}
                className="flex items-center gap-2"
              >
                {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                {currentStep === 3 ? "Go to Data Chat" : "Continue"}
                {!isLoading && <ArrowRight className="h-4 w-4" />}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Profile Analytics */}
            {activeProfile ? (
              <div className="space-y-8">
                <ProfileAnalytics profile={activeProfile} />

                {/* Profile Comparison */}
                <ProfileComparison
                  profiles={profiles}
                  activeProfile={activeProfile}
                  onProfileSwitch={async (profileId) => {
                    try {
                      await switchProfile(profileId);
                    } catch (error) {
                      console.error('Error switching profile:', error);
                    }
                  }}
                />
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">
                    Create a business profile to view analytics
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="mcp" className="space-y-6">
            {/* MCP Server Management */}
            <MCPServerDashboard
              onCreateServer={() => {
                setShowMCPConfig(true);
                setMcpEditServer(null);
              }}
              onEditServer={(server) => {
                setShowMCPConfig(true);
                setMcpEditServer(server);
              }}
            />
          </TabsContent>
        </Tabs>

        {/* Dialog Components */}
        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Data Source</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete "{selectedDataSource?.name}"? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => selectedDataSource && handleDeleteDataSource(selectedDataSource)}
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Business Profile Confirmation Dialog */}
        <Dialog open={isProfileDeleteDialogOpen} onOpenChange={setIsProfileDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Business Profile</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete "{activeProfile?.name}"? This action cannot be undone and will permanently remove all associated data sources and configurations.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsProfileDeleteDialogOpen(false)}
                disabled={isDeletingProfile}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteProfile}
                disabled={isDeletingProfile}
              >
                {isDeletingProfile ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Profile
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* View Data Source Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Data Source Details</DialogTitle>
            </DialogHeader>
            {selectedDataSource && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Name</label>
                    <p className="text-sm">{selectedDataSource.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Type</label>
                    <p className="text-sm">{getDataSourceTypeLabel(selectedDataSource.type)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <Badge variant={selectedDataSource.is_active ? "default" : "secondary"}>
                      {selectedDataSource.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Created</label>
                    <p className="text-sm">{formatDate(selectedDataSource.created_at)}</p>
                  </div>
                </div>
                {selectedDataSource.description && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Description</label>
                    <p className="text-sm">{selectedDataSource.description}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-gray-500">Configuration</label>
                  <div className="bg-gray-50 rounded-lg p-3 mt-1">
                    <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                      {JSON.stringify(selectedDataSource.metadata, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Data Source Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Data Source</DialogTitle>
              <DialogDescription>
                Update the details of your data source.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <Input
                  value={editFormData.name || ""}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Data source name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <Input
                  value={editFormData.description || ""}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Description (optional)"
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={editFormData.is_active || false}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="rounded"
                />
                <label htmlFor="is_active" className="text-sm font-medium">
                  Active
                </label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateDataSource}>
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* File Delete Warning Dialog */}
        <Dialog open={isFileDeleteWarningOpen} onOpenChange={setIsFileDeleteWarningOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-500" />
                Delete File with Related Data Sources
              </DialogTitle>
              <DialogDescription>
                The file "{fileToDelete?.name}" is being used by {relatedDatasources.length} data source{relatedDatasources.length > 1 ? 's' : ''}:
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-3">
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <div className="space-y-2">
                  {relatedDatasources.map((ds) => (
                    <div key={ds.id} className="flex items-center gap-2 text-sm">
                      <Database className="h-4 w-4 text-orange-600" />
                      <span className="font-medium">{ds.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {ds.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
              <p className="text-sm text-gray-600">
                What would you like to do?
              </p>
            </div>
            <DialogFooter className="flex-col gap-2 sm:flex-row">
              <Button
                variant="outline"
                onClick={() => setIsFileDeleteWarningOpen(false)}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                variant="secondary"
                onClick={() => handleConfirmFileDelete(false)}
                className="w-full sm:w-auto"
              >
                Delete File Only
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleConfirmFileDelete(true)}
                className="w-full sm:w-auto"
              >
                Delete File & Data Sources
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Profile Modal */}
        {activeProfile && (
          <EditProfileModal
            isOpen={isEditProfileModalOpen}
            onClose={() => setIsEditProfileModalOpen(false)}
            profileId={activeProfile.id}
            onProfileUpdated={(updatedProfile) => {
              // Refresh profiles to get updated data
              refreshProfiles();
            }}
          />
        )}

        {/* Enhanced MCP Server Configuration Modal */}
        {showMCPConfig && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <MCPServerConfig
                  onServerCreated={(server) => {
                    setShowMCPConfig(false);
                    toast({
                      title: "MCP Server Created",
                      description: `Server "${server.name}" has been created successfully.`
                    });
                    // Refresh data sources if needed
                    refresh({ source: 'mcp-server-create' });
                  }}
                  onCancel={() => setShowMCPConfig(false)}
                  initialData={mcpEditServer}
                  mode={mcpEditServer ? 'edit' : 'create'}
                />
              </div>
            </div>
          </div>
        )}

        {/* MCP Server Dashboard Modal */}
        {showMCPDashboard && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold">MCP Server Management</h2>
                  <Button variant="outline" onClick={() => setShowMCPDashboard(false)}>
                    Close
                  </Button>
                </div>
                <MCPServerDashboard
                  onCreateServer={() => {
                    setShowMCPDashboard(false);
                    setShowMCPConfig(true);
                    setMcpEditServer(null);
                  }}
                  onEditServer={(server) => {
                    setShowMCPDashboard(false);
                    setShowMCPConfig(true);
                    setMcpEditServer(server);
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </DashboardLayout>
  );
};

export default DataIntegration;
