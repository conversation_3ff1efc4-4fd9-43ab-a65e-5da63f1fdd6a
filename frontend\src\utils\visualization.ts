/**
 * Visualization utilities for rendering data visualizations.
 */

export type VisualizationType = 'chart' | 'interactive_chart' | 'table' | 'tree' | 'network' | 'heatmap' | 'custom' | 'classification' | 'marketing' | 'data_preview' | 'analysis_result';

export interface VisualizationData {
  type: VisualizationType;
  title?: string;
  description?: string;
  data: any;
  config?: any;
  chart_type?: string; // For interactive charts
}

/**
 * Validates visualization data to ensure it has the required properties.
 */
export function validateVisualization(visualization: any): VisualizationData | null {
  console.log('🔍 validateVisualization called with:', visualization);
  console.log('🔍 visualization type:', visualization?.type);
  console.log('🔍 visualization data:', visualization?.data);

  if (!visualization || typeof visualization !== 'object') {
    console.error('❌ Invalid visualization data: not an object');
    return null;
  }

  if (!visualization.type || !visualization.data) {
    console.error('❌ Invalid visualization data: missing required properties');
    return null;
  }

  // Validate based on type
  switch (visualization.type) {
    case 'chart':
      console.log('🔍 Validating chart data...');
      if (!validateChartData(visualization.data)) {
        console.error('❌ Chart data validation failed');
        return null;
      }
      break;
    case 'interactive_chart':
      console.log('🔍 Validating interactive chart data...');
      if (!validateInteractiveChartData(visualization.data)) {
        console.error('❌ Interactive chart data validation failed');
        return null;
      }
      break;
    case 'table':
      if (!validateTableData(visualization.data)) {
        return null;
      }
      break;
    case 'classification':
      // Basic validation for classification data
      if (!visualization.data || !visualization.data.results) {
        console.error('Invalid classification data: missing results');
        return null;
      }
      break;
    case 'marketing':
      // Basic validation for marketing content data
      if (!visualization.data) {
        console.error('Invalid marketing content data: missing data');
        return null;
      }
      break;
    case 'data_preview':
      // Basic validation for data preview
      if (!visualization.data || (!visualization.data.preview_data && !visualization.data.description)) {
        console.error('Invalid data preview: missing preview data or description');
        return null;
      }
      break;
    case 'tree':
    case 'network':
    case 'heatmap':
    case 'custom':
      // Basic validation for other types
      if (!visualization.data) {
        console.error(`Invalid ${visualization.type} data: missing data`);
        return null;
      }
      break;
    default:
      console.error(`Unknown visualization type: ${visualization.type}`);
      return null;
  }

  return visualization as VisualizationData;
}

/**
 * Validates chart data.
 */
function validateChartData(data: any): boolean {
  if (!data) {
    console.error('Invalid chart data: missing data');
    return false;
  }

  // If it's an image-based visualization, it should have an image property
  if (data.image) {
    return true;
  }

  // For standard chart data
  if (!data.labels || !Array.isArray(data.labels)) {
    console.error('Invalid chart data: missing or invalid labels');
    return false;
  }

  if (!data.datasets || !Array.isArray(data.datasets)) {
    console.error('Invalid chart data: missing or invalid datasets');
    return false;
  }

  return true;
}

/**
 * Validates interactive chart data.
 */
function validateInteractiveChartData(data: any): boolean {
  console.log('🔍 validateInteractiveChartData called with data:', data);
  console.log('🔍 data keys:', Object.keys(data || {}));

  if (!data) {
    console.error('Invalid interactive chart data: missing data');
    return false;
  }

  // Interactive chart data should have chart_data array
  if (data.chart_data && Array.isArray(data.chart_data)) {
    console.log('✅ Valid interactive chart data: found chart_data array with', data.chart_data.length, 'items');
    return true;
  }

  // Fallback to image-based validation
  if (data.image || data.fallback_image) {
    console.log('✅ Valid interactive chart data: found fallback image');
    return true;
  }

  console.error('❌ Invalid interactive chart data: missing chart_data or image');
  console.error('❌ Available data keys:', Object.keys(data || {}));
  return false;
}

/**
 * Validates table data.
 */
function validateTableData(data: any): boolean {
  if (!data) {
    console.error('Invalid table data: missing data');
    return false;
  }

  if (!data.headers || !Array.isArray(data.headers)) {
    console.error('Invalid table data: missing or invalid headers');
    return false;
  }

  if (!data.rows || !Array.isArray(data.rows)) {
    console.error('Invalid table data: missing or invalid rows');
    return false;
  }

  return true;
}

/**
 * Extracts all visualizations from metadata content.
 */
export function extractAllVisualizations(metadata: any): VisualizationData[] {
  if (!metadata?.content || !Array.isArray(metadata.content)) {
    return [];
  }

  const visualizations: VisualizationData[] = [];

  // Extract all image visualizations
  const imageItems = metadata.content.filter((item: any) => item.type === 'image' && item.src);

  imageItems.forEach((item: any, index: number) => {
    // Try to extract a more specific title for each visualization
    let title = `Chart ${index + 1}`;
    let description = `Generated visualization ${index + 1}`;

    // Look for text content that might describe this specific chart
    const textItems = metadata.content.filter((contentItem: any) => contentItem.type === 'text' && contentItem.text);

    if (textItems.length > 0) {
      // If we have text content, try to use it as context
      const allText = textItems.map((t: any) => t.text).join(' ');

      // Look for chart-specific patterns in the text
      const chartPatterns = [
        /scatter plot/i,
        /bar chart/i,
        /line chart/i,
        /pie chart/i,
        /histogram/i,
        /box plot/i,
        /heatmap/i,
        /correlation/i,
        /distribution/i,
        /trend/i
      ];

      for (const pattern of chartPatterns) {
        if (pattern.test(allText)) {
          const match = allText.match(pattern);
          if (match) {
            title = match[0].charAt(0).toUpperCase() + match[0].slice(1);
            break;
          }
        }
      }

      // Use the first sentence as description if available
      const firstSentence = allText.split('.')[0];
      if (firstSentence && firstSentence.length > 10 && firstSentence.length < 100) {
        description = firstSentence.trim();
      }
    }

    visualizations.push({
      type: 'chart',
      title: title,
      description: description,
      data: {
        image: item.src,
        metadata: item.metadata || {
          chart_type: 'static',
          query: metadata.prompt,
          timestamp: new Date().toISOString(),
          provider: 'pandasai'
        }
      }
    });
  });

  return visualizations;
}

/**
 * Processes raw visualization data from the API.
 */
export function processVisualizationData(metadata: any): VisualizationData | null {
  if (!metadata) {
    return null;
  }

  console.log('🔍 processVisualizationData called with metadata:', metadata);
  console.log('🔍 Full metadata structure:', JSON.stringify(metadata, null, 2));

  // 🚨 PRIORITY CHANGE: Now prioritize static images over interactive charts
  console.log('🚨 PRIORITY CHECK: Looking for static image data first');
  console.log('🔍 Metadata keys:', Object.keys(metadata || {}));
  console.log('🔍 Content data:', metadata.content);

  // 🚨 NEW PRIORITY: Check for static image content first
  if (metadata.content && Array.isArray(metadata.content)) {
    console.log('Processing metadata content for static images:', metadata.content);

    // For multiple images, we'll handle this in the VisualizationRenderer
    // Here we just process the first image for backward compatibility
    const imageContent = metadata.content.find(item => item.type === 'image' && item.src);
    if (imageContent) {
      console.log('✅ STATIC IMAGE FOUND - Prioritizing static visualization');
      console.log('Image src preview:', imageContent.src.substring(0, 50) + '...');

      // Extract title from text content if available
      const textContent = metadata.content.find(item => item.type === 'text' && item.text);
      const title = textContent?.text || metadata.prompt || 'Data Visualization';

      // Create static chart visualization (no longer checking for interactive data)
      console.log('Creating static chart visualization with enhanced features');
      const chartVisualization = {
        type: 'chart',
        title: title,
        description: 'Generated using PandasAI analysis',
        data: {
          image: imageContent.src,
          metadata: {
            chart_type: detectChartTypeFromContext(metadata),
            query: metadata.prompt,
            timestamp: new Date().toISOString(),
            provider: 'pandasai'
          }
        }
      };

      console.log('✅ Created static chart visualization:', chartVisualization);
      return validateVisualization(chartVisualization);
    }
  }
  // Handle direct visualization object - prioritize static images
  if (metadata.visualization && typeof metadata.visualization === 'object') {
    console.log('Found visualization object in metadata:', metadata.visualization);
    console.log('🔍 visualization type:', metadata.visualization.type);
    console.log('🔍 visualization data keys:', Object.keys(metadata.visualization.data || {}));

    // 🚨 NEW PRIORITY: Check for static image first, even in visualization objects
    if (metadata.visualization.data?.image) {
      console.log('✅ Found static image in visualization object - using static chart');
      const staticVisualization = {
        type: 'chart',
        title: metadata.visualization.title || 'Data Visualization',
        description: metadata.visualization.description || 'Generated data visualization',
        data: {
          image: metadata.visualization.data.image,
          metadata: {
            chart_type: detectChartTypeFromContext(metadata),
            query: metadata.prompt,
            timestamp: new Date().toISOString(),
            provider: 'pandasai'
          }
        }
      };
      console.log('Creating static visualization from visualization object:', staticVisualization);
      return validateVisualization(staticVisualization);
    }

    // Only fall back to interactive charts if no static image is available
    if (metadata.visualization.type === 'interactive_chart') {
      console.log('⚠️ Fallback: Direct interactive chart found (no static image available)');
      return validateVisualization(metadata.visualization);
    }

    // Default handling for other visualization types
    return validateVisualization(metadata.visualization);
  }

  // This section is now handled above in the priority check for static images
  // Keeping this as a fallback for any edge cases

  // 🚨 FALLBACK: Only use interactive charts if no static images are available
  // Check for interactive chart data from backend as last resort
  if (metadata.interactive_chart && metadata.interactive_chart.chart_data) {
    console.log('⚠️ FALLBACK: No static images found, using interactive chart data');
    console.log('🎨 Chart data length:', metadata.interactive_chart.chart_data.length);
    console.log('🎨 Chart type:', metadata.interactive_chart.chart_type);
    const backendChart = createInteractiveChartFromBackendData(metadata);
    if (backendChart) {
      console.log('✅ Created fallback interactive chart');
      return backendChart;
    }
  }

  // Additional fallback to metadata parsing for interactive charts
  const interactiveChart = createInteractiveChartFromMetadata(metadata);
  if (interactiveChart) {
    console.log('⚠️ FALLBACK: Created interactive chart from metadata parsing');
    return interactiveChart;
  }

  // Handle classification results
  if (metadata.sample_results && metadata.classification_type) {
    return validateVisualization({
      type: 'classification',
      title: 'Classification Results',
      description: `${metadata.sample_results.length} texts classified using ${metadata.classification_type === 'llm' ? 'LLM' : 'Hugging Face'} classification`,
      data: {
        results: metadata.sample_results,
        classification_type: metadata.classification_type
      }
    });
  }

  // Handle marketing content
  if (metadata.task_type && metadata.generated_content) {
    // Use the response field which contains the actual content
    return validateVisualization({
      type: 'marketing',
      title: `${metadata.task_type.replace(/_/g, ' ')} Content`,
      description: 'Generated marketing content',
      data: {
        content: metadata.response || metadata.content || '',
        task_type: metadata.task_type,
        canExport: true
      }
    });
  }

  // Handle data preview
  if (metadata.data_preview || metadata.data_profile) {
    const previewType = metadata.data_preview ? 'preview' : 'profile';
    const previewData = metadata.data_preview || metadata.data_profile;

    // Extract preview data from the metadata
    let dataToDisplay = null;
    let columns = [];

    if (previewData.metadata && previewData.metadata.preview_data) {
      dataToDisplay = previewData.metadata.preview_data;
      columns = Object.keys(dataToDisplay[0] || {});
    } else if (previewData.metadata && previewData.metadata.filtered_data) {
      dataToDisplay = previewData.metadata.filtered_data;
      columns = Object.keys(dataToDisplay[0] || {});
    } else if (previewData.metadata && previewData.metadata.columns) {
      columns = previewData.metadata.columns;
    }

    // Get the description text
    let descriptionText = '';
    if (previewData.content && previewData.content.length > 0) {
      descriptionText = previewData.content.map((item: any) =>
        item.type === 'text' ? item.text : ''
      ).join('\n');
    }

    return validateVisualization({
      type: 'data_preview',
      title: `Data ${previewType === 'preview' ? 'Preview' : 'Profile'}`,
      description: `${previewType === 'preview' ? 'First few rows' : 'Statistical summary'} of the data`,
      data: {
        preview_data: dataToDisplay,
        columns: columns,
        description: descriptionText,
        metadata: previewData.metadata || {}
      }
    });
  }

  // Handle data info (for analysis agent responses)
  if (metadata.data_info) {
    const dataInfo = metadata.data_info;

    // Extract info data from the metadata
    let infoData = null;
    let columns = [];

    if (dataInfo.metadata) {
      columns = dataInfo.metadata.columns || [];
      infoData = {
        shape: dataInfo.metadata.shape,
        dtypes: dataInfo.metadata.dtypes,
        missing_values: dataInfo.metadata.missing_values
      };
    }

    // Get the description text
    let descriptionText = '';
    if (dataInfo.content && dataInfo.content.length > 0) {
      descriptionText = dataInfo.content.map((item: any) =>
        item.type === 'text' ? item.text : ''
      ).join('\n');
    }

    return validateVisualization({
      type: 'data_preview',
      title: 'Data Information',
      description: 'Dataset structure and information',
      data: {
        preview_data: null,
        columns: columns,
        description: descriptionText,
        metadata: infoData || {}
      }
    });
  }

  // Handle table content embedded in response content
  if (metadata.content && Array.isArray(metadata.content)) {
    const tableContent = metadata.content.find(item => item.type === 'table');
    if (tableContent) {
      // Handle different table formats
      let headers = [];
      let rows = [];

      if (tableContent.table) {
        headers = tableContent.table.headers || [];
        rows = tableContent.table.rows || [];
      } else if (tableContent.data && tableContent.columns) {
        // Handle PandasAI format
        headers = tableContent.columns;
        rows = tableContent.data.map(row => headers.map(col => row[col] || ''));
      }

      if (headers.length > 0 && rows.length > 0) {
        return validateVisualization({
          type: 'table',
          title: metadata.title || 'Analysis Results',
          description: metadata.description || 'Data analysis table',
          data: {
            headers: headers,
            rows: rows
          }
        });
      }
    }
  }

  // Handle string-based visualization types (legacy format)
  if (metadata.visualization && typeof metadata.visualization === 'string') {
    // Convert legacy format to new format
    const type = metadata.visualization as VisualizationType;
    let data: any;

    switch (type) {
      case 'chart':
        data = {
          labels: metadata.chart_labels || [],
          datasets: metadata.chart_datasets || [],
        };
        break;
      case 'table':
        data = {
          headers: metadata.table_headers || [],
          rows: metadata.table_rows || [],
        };
        break;
      default:
        data = metadata.visualization_data || {};
    }

    return validateVisualization({
      type,
      title: metadata.visualization_title,
      description: metadata.visualization_description,
      data,
      config: metadata.visualization_config,
    });
  }

  // Handle analysis results with mixed content (text, tables, images)
  if (metadata.content && Array.isArray(metadata.content) && metadata.content.length > 0) {
    const hasMultipleContentTypes = metadata.content.some(item =>
      ['table', 'image', 'text'].includes(item.type)
    );

    if (hasMultipleContentTypes) {
      return validateVisualization({
        type: 'analysis_result',
        title: metadata.title || 'Analysis Results',
        description: metadata.description || 'Comprehensive analysis output',
        data: {
          content: metadata.content,
          metadata: metadata
        }
      });
    }
  }

  return null;
}

/**
 * REMOVED: Sample chart visualization function has been removed.
 * The system now uses real data from the backend exclusively.
 */

/**
 * REMOVED: Sample table visualization function has been removed.
 * The system now uses real data from the backend exclusively.
 */

/**
 * CRITICAL FUNCTION: Create interactive chart from any available metadata
 * This function aggressively tries to extract data for interactive charts
 */
function createInteractiveChartFromMetadata(metadata: any, fallbackImage?: string): VisualizationData | null {
  console.log('🔍 Attempting to create interactive chart from metadata:', metadata);

  // Priority 1: Check for explicit interactive chart data
  if (metadata.chart_data || metadata.data?.chart_data) {
    const chartData = metadata.chart_data || metadata.data.chart_data;
    console.log('✅ Found explicit chart_data:', chartData);

    return {
      type: 'interactive_chart',
      title: metadata.title || metadata.prompt || 'Interactive Chart',
      description: metadata.description || 'Interactive data visualization',
      data: chartData,
      chart_type: chartData.chart_type || metadata.chart_type || 'bar',
      config: { responsive: true, interactive: true }
    };
  }

  // Priority 2: Check for PandasAI results with data
  if (metadata.content && Array.isArray(metadata.content)) {
    // Look for data content in the response
    const dataContent = metadata.content.find((item: any) =>
      item.type === 'data' ||
      (item.type === 'text' && item.data) ||
      (item.text && typeof item.text === 'object')
    );

    if (dataContent) {
      console.log('✅ Found data content in PandasAI response:', dataContent);

      const data = dataContent.data || dataContent.text || dataContent;
      return {
        type: 'interactive_chart',
        title: metadata.prompt || 'Data Analysis Chart',
        description: 'Generated from PandasAI analysis',
        data: {
          chart_data: Array.isArray(data) ? data : [data],
          chart_type: 'bar',
          metadata: {
            x_axis: 'name',
            y_axes: ['value']
          }
        },
        chart_type: 'bar',
        config: { responsive: true, interactive: true }
      };
    }
  }

  // Priority 3: No more fake data generation - return null if no real data
  console.log('❌ No real data available for interactive chart - fake data generation removed');

  console.log('❌ Could not create interactive chart from metadata');
  return null;
}

/**
 * Create interactive chart from backend data (NEW: Uses real backend data)
 */
function createInteractiveChartFromBackendData(metadata: any): VisualizationData | null {
  console.log('🎨 Creating interactive chart from backend data:', metadata.interactive_chart);

  const interactiveData = metadata.interactive_chart;
  if (!interactiveData || !interactiveData.chart_data || !Array.isArray(interactiveData.chart_data)) {
    console.log('❌ Invalid backend interactive chart data');
    console.log('🔍 Interactive data structure:', interactiveData);
    return null;
  }

  const chartData = interactiveData.chart_data;
  const chartType = interactiveData.chart_type || 'bar';
  const chartMetadata = interactiveData.metadata || {};

  console.log('✅ Backend chart data:', {
    type: chartType,
    records: chartData.length,
    metadata: chartMetadata,
    sampleData: chartData.slice(0, 2) // Log first 2 records for debugging
  });

  // Get fallback image if available
  const fallbackImage = metadata.visualization?.data?.fallback_image ||
                       metadata.visualization?.data?.image;

  return {
    type: 'interactive_chart',
    title: metadata.prompt || chartMetadata.title || interactiveData.title || 'Data Visualization',
    description: interactiveData.description || `Interactive ${chartType} chart with ${chartData.length} data points`,
    data: {
      chart_data: chartData,
      chart_type: chartType,
      columns: chartMetadata.columns || interactiveData.columns || [],
      metadata: {
        ...chartMetadata,
        data_source: 'backend_generated',
        total_records: chartData.length
      },
      fallback_image: fallbackImage
    },
    chart_type: chartType,
    config: {
      responsive: true,
      interactive: true,
      ...interactiveData.config
    }
  };
}

/**
 * Detect chart type from prompt/context
 */
function detectChartTypeFromContext(metadata: any): string {
  // Safely convert to strings and get all relevant text
  const prompt = String(metadata.prompt || metadata.title || '').toLowerCase();
  const content = String(metadata.content || '').toLowerCase();
  const description = String(metadata.description || '').toLowerCase();
  const fullText = `${prompt} ${content} ${description}`;

  console.log('🔍 Detecting chart type from context:', fullText);
  console.log('🔍 Raw metadata:', metadata);

  // PIE CHART - Highest priority for distribution/proportion data
  if (fullText.includes('pie chart') || fullText.includes('pie') ||
      (fullText.includes('distribution') && (fullText.includes('gender') || fullText.includes('category'))) ||
      fullText.includes('proportion') || fullText.includes('percentage') || fullText.includes('share')) {
    console.log('✅ Detected chart type: PIE');
    return 'pie';
  }

  // SCATTER CHART - Only for correlation/relationship data
  if (fullText.includes('scatter') || fullText.includes('correlation') ||
      fullText.includes('relationship') || fullText.includes('vs') || fullText.includes('against')) {
    console.log('✅ Detected chart type: SCATTER');
    return 'scatter';
  }

  // HISTOGRAM/BAR - For frequency distributions
  if (fullText.includes('histogram') || fullText.includes('frequency') || fullText.includes('count')) {
    console.log('✅ Detected chart type: BAR (histogram)');
    return 'bar';
  }

  // LINE CHART - For trends over time
  if (fullText.includes('line') || fullText.includes('trend') || fullText.includes('time') || fullText.includes('over time')) {
    console.log('✅ Detected chart type: LINE');
    return 'line';
  }

  // AREA CHART
  if (fullText.includes('area') || fullText.includes('filled')) {
    console.log('✅ Detected chart type: AREA');
    return 'area';
  }

  // Default to bar chart for general categorical data
  console.log('✅ Detected chart type: BAR (default)');
  return 'bar';
}
